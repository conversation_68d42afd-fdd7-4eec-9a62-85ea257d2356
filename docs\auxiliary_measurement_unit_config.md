# 辅助计量单位配置化管理实现文档

## 概述

本次修改实现了辅助计量单位的配置化管理，允许通过配置表来灵活设置不同客户、集团、产品的辅助计量单位，替代原有的硬编码逻辑。

## 修改内容

### 1. 实体类修改

- **OutboundInformationTransferRecord**: 已存在`auxiliaryMeasurementUnit`字段（第91行）

### 2. 新建配置实体类和相关文件

#### 2.1 实体类
- `AuxiliaryMeasurementUnitConfig.java` - 辅助计量单位配置实体类

#### 2.2 数据访问层
- `AuxiliaryMeasurementUnitConfigMapper.java` - Mapper接口
- `AuxiliaryMeasurementUnitConfigMapper.xml` - MyBatis映射文件

#### 2.3 业务逻辑层
- `AuxiliaryMeasurementUnitConfigService.java` - Service接口
- `AuxiliaryMeasurementUnitConfigServiceImpl.java` - Service实现类

### 3. 数据库表结构

```sql
CREATE TABLE `cmf_auxiliary_measurement_unit_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `group_code` varchar(50) DEFAULT NULL COMMENT '集团编号',
  `group_id` int(11) DEFAULT NULL COMMENT '集团ID',
  `customer_code` varchar(50) DEFAULT NULL COMMENT '客户编号',
  `product_category_code` varchar(50) DEFAULT NULL COMMENT '产品类别编号',
  `product_category_name` varchar(100) DEFAULT NULL COMMENT '产品类别名称',
  `product_code` varchar(50) DEFAULT NULL COMMENT '产品编号',
  `product_name` varchar(100) DEFAULT NULL COMMENT '产品名称',
  `auxiliary_measurement_unit` varchar(50) NOT NULL COMMENT '辅助计量单位',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='辅助计量单位配置表';
```

### 4. 优先级规则

配置表查询优先级（从高到低）：
1. 客户编号 + 产品编号
2. 客户编号 + 产品类别编号
3. 集团编号 + 产品编号
4. 集团ID + 产品编号
5. 集团编号 + 产品类别编号
6. 集团ID + 产品类别编号

### 5. productionPackagingSpecification字段赋值逻辑

按以下优先级顺序：
1. **配置表查询**: 根据客户和产品信息匹配最高优先级的配置
2. **auxiliaryMeasurementUnit字段**: 如果配置表无匹配且该字段有内容，则使用该字段值
3. **原有逻辑**: 如果以上都无内容，则使用原有逻辑（数值>100时添加"/包"，否则添加"/袋"）

### 6. 修改的业务逻辑

#### 6.1 TransferRecordServiceImpl修改

- 添加了`AuxiliaryMeasurementUnitConfigService`的依赖注入
- 新增`getPackagingSpecificationWithAuxiliaryUnit`方法实现新的优先级逻辑
- 修改`getTransferRecordDetails`方法中的包装规格设置逻辑
- 简化`creatTransportReport`方法中的包装规格处理（移除重复逻辑）

#### 6.2 核心方法

```java
private String getPackagingSpecificationWithAuxiliaryUnit(
        String customerNumber,
        String productNumber,
        String productCategoryNumber,
        String auxiliaryMeasurementUnit,
        String materialPackagingSpecification)
```

## 使用示例

### 1. 配置数据示例

```sql
-- 客户级别配置
INSERT INTO cmf_auxiliary_measurement_unit_config 
(customer_code, product_code, auxiliary_measurement_unit, remark) 
VALUES ('CUST001', 'PROD001', '25KG/袋', '客户CUST001的产品PROD001专用配置');

-- 集团级别配置
INSERT INTO cmf_auxiliary_measurement_unit_config 
(group_code, product_category_code, auxiliary_measurement_unit, remark) 
VALUES ('GRP001', 'CAT001', '50KG/包', '集团GRP001的化工产品类别配置');
```

### 2. API调用示例

```java
@Autowired
private AuxiliaryMeasurementUnitConfigService auxiliaryMeasurementUnitConfigService;

// 查询辅助计量单位
String unit = auxiliaryMeasurementUnitConfigService.getAuxiliaryMeasurementUnitByPriority(
    "CUST001", "GRP001", 1, "PROD001", "CAT001");
```

## 测试

- 创建了`AuxiliaryMeasurementUnitConfigServiceTest`测试类
- 包含基本的查询和保存功能测试

## 部署说明

1. 执行`database/auxiliary_measurement_unit_config.sql`创建数据库表
2. 重新编译并部署应用
3. 根据业务需求配置相应的辅助计量单位数据

## 注意事项

1. 配置表中的`auxiliary_measurement_unit`字段不能为空
2. 优先级规则严格按照客户 > 集团、产品 > 产品类别的顺序
3. 如果没有找到任何配置，系统会回退到原有的逻辑
4. 建议在生产环境部署前充分测试各种配置场景
