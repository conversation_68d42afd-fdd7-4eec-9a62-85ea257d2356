package com.haihang.model.DO.outbound;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Description: 辅助计量单位配置
 * @Author: zad
 * @Create: 2025-01-21
 */
@Data
@Accessors(chain = true)
@TableName(value = "cmf_auxiliary_measurement_unit_config")
public class AuxiliaryMeasurementUnitConfig {
    
    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Integer id;
    
    /**
     * 集团编号（对应Group实体）
     */
    @TableField(value = "group_code")
    private String groupCode;
    
    /**
     * 集团ID（对应TransportReportGroup实体）
     */
    @TableField(value = "group_id")
    private Integer groupId;
    
    /**
     * 客户编号
     */
    @TableField(value = "customer_code")
    private String customerCode;
    
    /**
     * 产品类别编号
     */
    @TableField(value = "product_category_code")
    private String productCategoryCode;
    
    /**
     * 产品类别名称
     */
    @TableField(value = "product_category_name")
    private String productCategoryName;
    
    /**
     * 产品编号
     */
    @TableField(value = "product_code")
    private String productCode;
    
    /**
     * 产品名称
     */
    @TableField(value = "product_name")
    private String productName;
    
    /**
     * 辅助计量单位
     */
    @TableField(value = "auxiliary_measurement_unit")
    private String auxiliaryMeasurementUnit;
    
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private java.time.LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private java.time.LocalDateTime updateTime;
    
    /**
     * 是否删除（0-未删除，1-已删除）
     */
    @TableField(value = "is_deleted")
    private Boolean isDeleted;
    
    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;
}
