package com.haihang.mapper.outbound;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haihang.model.DO.outbound.AuxiliaryMeasurementUnitConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 辅助计量单位配置Mapper
 * @Author: zad
 * @Create: 2025-01-21
 */
@Mapper
public interface AuxiliaryMeasurementUnitConfigMapper extends BaseMapper<AuxiliaryMeasurementUnitConfig> {
    
    /**
     * 根据优先级查询辅助计量单位配置
     * 优先级规则：客户编号 > 集团编号或集团ID；产品编号 > 产品类别编号
     * 
     * @param customerCode 客户编号
     * @param groupCode 集团编号
     * @param groupId 集团ID
     * @param productCode 产品编号
     * @param productCategoryCode 产品类别编号
     * @return 辅助计量单位配置列表（按优先级排序）
     */
    List<AuxiliaryMeasurementUnitConfig> selectByPriority(
            @Param("customerCode") String customerCode,
            @Param("groupCode") String groupCode,
            @Param("groupId") Integer groupId,
            @Param("productCode") String productCode,
            @Param("productCategoryCode") String productCategoryCode
    );
}
