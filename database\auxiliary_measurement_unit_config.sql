-- 辅助计量单位配置表
CREATE TABLE `cmf_auxiliary_measurement_unit_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `group_code` varchar(50) DEFAULT NULL COMMENT '集团编号（对应Group实体）',
  `group_id` int(11) DEFAULT NULL COMMENT '集团ID（对应TransportReportGroup实体）',
  `customer_code` varchar(50) DEFAULT NULL COMMENT '客户编号',
  `product_category_code` varchar(50) DEFAULT NULL COMMENT '产品类别编号',
  `product_category_name` varchar(100) DEFAULT NULL COMMENT '产品类别名称',
  `product_code` varchar(50) DEFAULT NULL COMMENT '产品编号',
  `product_name` varchar(100) DEFAULT NULL COMMENT '产品名称',
  `auxiliary_measurement_unit` varchar(50) NOT NULL COMMENT '辅助计量单位',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除（0-未删除，1-已删除）',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_customer_product` (`customer_code`, `product_code`),
  KEY `idx_customer_category` (`customer_code`, `product_category_code`),
  KEY `idx_group_product` (`group_code`, `product_code`),
  KEY `idx_group_category` (`group_code`, `product_category_code`),
  KEY `idx_group_id_product` (`group_id`, `product_code`),
  KEY `idx_group_id_category` (`group_id`, `product_category_code`),
  KEY `idx_is_deleted` (`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='辅助计量单位配置表';

-- 插入示例数据
INSERT INTO `cmf_auxiliary_measurement_unit_config` 
(`group_code`, `group_id`, `customer_code`, `product_category_code`, `product_category_name`, `product_code`, `product_name`, `auxiliary_measurement_unit`, `remark`) 
VALUES 
-- 客户级别配置示例
('GRP001', 1, 'CUST001', NULL, NULL, 'PROD001', '产品A', '25KG/袋', '客户CUST001的产品PROD001专用配置'),
('GRP001', 1, 'CUST001', 'CAT001', '化工产品', NULL, NULL, '50KG/包', '客户CUST001的化工产品类别配置'),

-- 集团级别配置示例
(NULL, NULL, NULL, NULL, NULL, 'PROD002', '产品B', '100KG/包', '产品B的通用配置'),
('GRP002', 2, NULL, 'CAT002', '橡胶助剂', NULL, NULL, '25KG/袋', '集团GRP002的橡胶助剂类别配置');
