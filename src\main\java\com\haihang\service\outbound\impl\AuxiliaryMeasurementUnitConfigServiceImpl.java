package com.haihang.service.outbound.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haihang.mapper.outbound.AuxiliaryMeasurementUnitConfigMapper;
import com.haihang.model.DO.outbound.AuxiliaryMeasurementUnitConfig;
import com.haihang.service.outbound.AuxiliaryMeasurementUnitConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 辅助计量单位配置Service实现类
 * @Author: zad
 * @Create: 2025-01-21
 */
@Slf4j
@Service
public class AuxiliaryMeasurementUnitConfigServiceImpl 
        extends ServiceImpl<AuxiliaryMeasurementUnitConfigMapper, AuxiliaryMeasurementUnitConfig> 
        implements AuxiliaryMeasurementUnitConfigService {

    @Override
    public String getAuxiliaryMeasurementUnitByPriority(
            String customerCode,
            String groupCode,
            Integer groupId,
            String productCode,
            String productCategoryCode) {
        
        log.info("查询辅助计量单位配置 - 客户编号: {}, 集团编号: {}, 集团ID: {}, 产品编号: {}, 产品类别编号: {}", 
                customerCode, groupCode, groupId, productCode, productCategoryCode);
        
        // 查询配置列表（按优先级排序）
        List<AuxiliaryMeasurementUnitConfig> configList = baseMapper.selectByPriority(
                customerCode, groupCode, groupId, productCode, productCategoryCode);
        
        if (CollUtil.isEmpty(configList)) {
            log.info("未找到匹配的辅助计量单位配置");
            return null;
        }
        
        // 返回第一个匹配的配置（优先级最高）
        AuxiliaryMeasurementUnitConfig config = configList.get(0);
        String auxiliaryMeasurementUnit = config.getAuxiliaryMeasurementUnit();
        
        log.info("找到匹配的辅助计量单位配置 - ID: {}, 辅助计量单位: {}", config.getId(), auxiliaryMeasurementUnit);
        
        return StrUtil.isNotBlank(auxiliaryMeasurementUnit) ? auxiliaryMeasurementUnit : null;
    }
}
