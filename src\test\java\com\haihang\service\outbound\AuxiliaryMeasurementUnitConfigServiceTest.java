package com.haihang.service.outbound;

import com.haihang.model.DO.outbound.AuxiliaryMeasurementUnitConfig;
import com.haihang.service.outbound.impl.AuxiliaryMeasurementUnitConfigServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import javax.annotation.Resource;

/**
 * @Description: 辅助计量单位配置Service测试类
 * @Author: zad
 * @Create: 2025-01-21
 */
@SpringBootTest
@SpringJUnitConfig
public class AuxiliaryMeasurementUnitConfigServiceTest {

    @Resource
    private AuxiliaryMeasurementUnitConfigService auxiliaryMeasurementUnitConfigService;

    @Test
    public void testGetAuxiliaryMeasurementUnitByPriority() {
        // 测试优先级查询
        String result = auxiliaryMeasurementUnitConfigService.getAuxiliaryMeasurementUnitByPriority(
                "CUST001", // 客户编号
                "GRP001",  // 集团编号
                1,         // 集团ID
                "PROD001", // 产品编号
                "CAT001"   // 产品类别编号
        );
        
        System.out.println("查询结果: " + result);
    }

    @Test
    public void testSaveConfig() {
        // 测试保存配置
        AuxiliaryMeasurementUnitConfig config = new AuxiliaryMeasurementUnitConfig();
        config.setCustomerCode("TEST001");
        config.setProductCode("TESTPROD001");
        config.setAuxiliaryMeasurementUnit("25KG/袋");
        config.setRemark("测试配置");
        
        boolean result = auxiliaryMeasurementUnitConfigService.save(config);
        System.out.println("保存结果: " + result);
    }
}
