<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haihang.mapper.outbound.AuxiliaryMeasurementUnitConfigMapper">

    <!-- 根据优先级查询辅助计量单位配置 -->
    <select id="selectByPriority" resultType="com.haihang.model.DO.outbound.AuxiliaryMeasurementUnitConfig">
        SELECT 
            id,
            group_code,
            group_id,
            customer_code,
            product_category_code,
            product_category_name,
            product_code,
            product_name,
            auxiliary_measurement_unit,
            create_time,
            update_time,
            is_deleted,
            remark
        FROM cmf_auxiliary_measurement_unit_config
        WHERE is_deleted = 0
        AND auxiliary_measurement_unit IS NOT NULL 
        AND auxiliary_measurement_unit != ''
        AND (
            <!-- 优先级1: 客户编号 + 产品编号 -->
            (customer_code = #{customerCode} AND product_code = #{productCode})
            OR
            <!-- 优先级2: 客户编号 + 产品类别编号 -->
            (customer_code = #{customerCode} AND product_category_code = #{productCategoryCode} AND product_code IS NULL)
            OR
            <!-- 优先级3: 集团编号 + 产品编号 -->
            (group_code = #{groupCode} AND product_code = #{productCode} AND customer_code IS NULL)
            OR
            <!-- 优先级4: 集团ID + 产品编号 -->
            (group_id = #{groupId} AND product_code = #{productCode} AND customer_code IS NULL AND group_code IS NULL)
            OR
            <!-- 优先级5: 集团编号 + 产品类别编号 -->
            (group_code = #{groupCode} AND product_category_code = #{productCategoryCode} AND customer_code IS NULL AND product_code IS NULL)
            OR
            <!-- 优先级6: 集团ID + 产品类别编号 -->
            (group_id = #{groupId} AND product_category_code = #{productCategoryCode} AND customer_code IS NULL AND group_code IS NULL AND product_code IS NULL)
        )
        ORDER BY 
            <!-- 按优先级排序 -->
            CASE 
                WHEN customer_code = #{customerCode} AND product_code = #{productCode} THEN 1
                WHEN customer_code = #{customerCode} AND product_category_code = #{productCategoryCode} AND product_code IS NULL THEN 2
                WHEN group_code = #{groupCode} AND product_code = #{productCode} AND customer_code IS NULL THEN 3
                WHEN group_id = #{groupId} AND product_code = #{productCode} AND customer_code IS NULL AND group_code IS NULL THEN 4
                WHEN group_code = #{groupCode} AND product_category_code = #{productCategoryCode} AND customer_code IS NULL AND product_code IS NULL THEN 5
                WHEN group_id = #{groupId} AND product_category_code = #{productCategoryCode} AND customer_code IS NULL AND group_code IS NULL AND product_code IS NULL THEN 6
                ELSE 999
            END ASC,
            id DESC
        LIMIT 1
    </select>

</mapper>
