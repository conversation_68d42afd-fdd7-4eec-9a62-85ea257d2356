package com.haihang.service.outbound;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haihang.model.DO.outbound.AuxiliaryMeasurementUnitConfig;

/**
 * @Description: 辅助计量单位配置Service
 * @Author: zad
 * @Create: 2025-01-21
 */
public interface AuxiliaryMeasurementUnitConfigService extends IService<AuxiliaryMeasurementUnitConfig> {
    
    /**
     * 根据优先级获取辅助计量单位
     * 优先级规则：客户编号 > 集团编号或集团ID；产品编号 > 产品类别编号
     * 
     * @param customerCode 客户编号
     * @param groupCode 集团编号
     * @param groupId 集团ID
     * @param productCode 产品编号
     * @param productCategoryCode 产品类别编号
     * @return 辅助计量单位，如果没有找到配置则返回null
     */
    String getAuxiliaryMeasurementUnitByPriority(
            String customerCode,
            String groupCode,
            Integer groupId,
            String productCode,
            String productCategoryCode
    );
}
